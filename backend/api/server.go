package api

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/vima-inc/derental/api/middleware"
	"github.com/vima-inc/derental/service"
)

// Server is a struct representing a http Server.
type Server struct {
	httpServer *http.Server
	service    *service.Service
	handler    *gin.Engine
}

// New creates a new Server instance.
func New(port string, service *service.Service, options ...Options) *Server {
	handler := gin.Default()
	handler.Use(middleware.Errors())

	// Configure trusted proxies for Cloud Run
	// Trust all proxies in development, in production this should be restricted to Google's IP ranges
	if err := handler.SetTrustedProxies([]string{"0.0.0.0/0"}); err != nil {
		fmt.Printf("Warning: Failed to set trusted proxies: %v\n", err)
	}

	s := &Server{
		service: service,
		httpServer: &http.Server{
			Addr: fmt.Sprintf(":%s", port),
		},
		handler: handler,
	}

	for _, o := range options {
		o(s)
	}

	return s
}

// StartHTTP start the http server.
func (s *Server) StartHTTP() error {
	s.httpServer.Handler = s.handler

	return s.httpServer.ListenAndServe()
}

// Shutdown shutdown the http server.
func (s *Server) Shutdown(ctx context.Context) error {
	return s.httpServer.Shutdown(ctx)
}

// WithDefaultRoutes sets the default routes.
func WithDefaultRoutes(auth gin.HandlerFunc) Options {
	return func(s *Server) {
		public := s.handler.Group("/public").Use(middleware.Latency())
		{
			public.POST("/signin", signin(s.service))
			public.POST("/signup", signup(s.service))
			public.GET("/exist/:email", existByEmail(s.service))

			public.
				Use(middleware.CacheControl(5*time.Second)).
				GET("/password/forgot/:email", forgotPassword(s.service))
			public.POST("/password/validate", validateToken(s.service))
			public.POST("/password/reset", resetPassword(s.service))

			public.
				Use(middleware.CacheControl(100*time.Second)).
				GET("/equipment/:equipment_id", getEquipment(s.service))

			public.
				Use(middleware.CacheControl(100*time.Second)).
				GET("/equipper/:equipper_id", getEquipperByID(s.service))
			public.GET("/equipper/email/:email", getEquipperByEmail(s.service))

			public.POST("/lead", addLead(s.service))

			public.GET("/tooler_bidz_equipment/:tooler_bidz_equipment_id", getBidzEquipmentByID(s.service))
			public.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/equipper/category", getAllEquippersByCategory(s.service))
			public.GET("/equipment/equipper", getEquipmentsByEquipperIDAndEquipmentName(s.service))
			public.GET("/equipper/user_name/:userName", getEquipperByUserName(s.service))
			public.GET("/equippers", getAllEquipper(s.service))
			public.GET("/equippers/country/:country", getEquippersByCountry(s.service))

			public.POST("/order/complete", handleOrderCompleteWebhook(s.service))
			public.POST("/order/notify", handleOrderCompleteNotification(s.service))
			public.GET("/country", getAllCountries())
		}

		authenticatedHandlers := s.handler.Group("/").Use(middleware.Latency()).Use(auth)
		{

			authenticatedHandlers.GET("/equipper", getEquipper(s.service))
			authenticatedHandlers.PUT("/equipper", updateEquipper(s.service))
			authenticatedHandlers.DELETE("/equipper", deleteEquipper(s.service))
			authenticatedHandlers.POST("/equipper/picture/upload", uploadProfilePicture(s.service))
			authenticatedHandlers.POST("/lodger/picture/upload", uploadLodgerProfilePicture(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/equipper/equipments", getEquipmentsByEquipperID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/equipper/equipments/booked", getBookedEquipmentsByEquipperID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/equipper/booked", getBookedEquipmentByStatusAndEquipperID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/lodger", getLodger(s.service))
			authenticatedHandlers.Use(middleware.CacheControl(2*time.Second)).GET(
				"/lodger/:id", getLodgerByID(s.service),
			)
			authenticatedHandlers.GET("/lodger/all", getAllLodgers(s.service))
			authenticatedHandlers.GET("/lodger/all/:country", GetAllLodgersByCountry(s.service))
			authenticatedHandlers.PUT("/lodger", updateLodger(s.service))
			authenticatedHandlers.DELETE("/lodger", deleteLodger(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/lodger/booking", getBookEquipmentByLodgerID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/lodger/booked/", getBookedEquipmentByStatusAndLodgerID(s.service))

			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/lodger/member", getMembersLodger(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/equipper/member", getMembersEquipper(s.service))
			authenticatedHandlers.POST("/member", addMember(s.service))
			authenticatedHandlers.PUT("/member/:member_id", updateMember(s.service))
			authenticatedHandlers.GET("/member/:member_id/:member_of", getMemberByID(s.service))
			authenticatedHandlers.GET("/member/selection_list/:member_id/:member_of", getSelectionList(s.service))
			authenticatedHandlers.DELETE("/member", deleteMember(s.service))
			authenticatedHandlers.GET("/member/verify_email/:email/:member_of", invitationExists(s.service))

			authenticatedHandlers.GET("/projects", getProjectByLodgerID(s.service))
			authenticatedHandlers.POST("/project", addProject(s.service))
			authenticatedHandlers.PUT("/project/:project_id", updateProject(s.service))
			authenticatedHandlers.DELETE("/project/:project_id", deleteProject(s.service))
			authenticatedHandlers.POST("/project/affect_one_equipment/:project_id", affectOneEquipment(s.service))
			authenticatedHandlers.POST("/project/affect_one_bidz_equipment/:project_id", affectOneBidzEquipment(s.service))
			authenticatedHandlers.POST("/project/affect_one_member/:project_id", affectOneMember(s.service))
			authenticatedHandlers.GET("/project/:project_id", getProjectByID(s.service))

			authenticatedHandlers.POST("/equipments/upload", uploadEquipments(s.service))
			authenticatedHandlers.POST("/equipment", addEquipment(s.service))
			authenticatedHandlers.POST("/equipment/upload_image/:equipment_id", uploadImageEquipment(s.service))
			authenticatedHandlers.PUT("/equipment/:equipment_id", updateEquipment(s.service))
			authenticatedHandlers.PUT("/equipment", updateBatchOfEquipmentByName(s.service))
			authenticatedHandlers.DELETE("/equipment/:equipment_id", deleteEquipment(s.service))
			authenticatedHandlers.DELETE("/equipment/all", deleteEquipmentsByEquipperID(s.service))
			authenticatedHandlers.POST("/equipment/synchronization", airTableSynchronization(s.service))
			authenticatedHandlers.POST("/equipment/:equipment_id", changeEquipmentStatus(s.service))

			authenticatedHandlers.POST("/equipment/book", bookEquipment(s.service))
			authenticatedHandlers.POST("/equipment/book/:book_equipment_id/accept", acceptBookEquipment(s.service))
			authenticatedHandlers.POST("/equipment/book/:book_equipment_id/reject", rejectBookEquipment(s.service))
			authenticatedHandlers.POST("/equipment/book/:book_equipment_id/cancel", cancelBookEquipment(s.service))
			authenticatedHandlers.POST("/equipment/book/:book_equipment_id/return", updateBookingEquipmentStatusAfterReturnEquipment(s.service))

			authenticatedHandlers.POST("/request_bids/send_bits_request", sendBitsRequest(s.service))
			authenticatedHandlers.POST("/request_bids/:bids_request_id/accept", acceptBidsRequest(s.service))
			authenticatedHandlers.POST("/request_bids/:bids_request_id/reject", rejectBidsRequest(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/request_bids", getAllBidsRequest(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/request_bids/equipper", getAllBidsRequestByEquipperID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/request_bids/lodger", getAllBidsRequestByLodgerID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/request_bids/:request_id", getBidsRequestID(s.service))
			authenticatedHandlers.POST("/credit_check_form/add", addCreditCheckForm(s.service))
			authenticatedHandlers.GET("/credit_check_form/:credit_check_form_id", getCreditCheckFormByID(s.service))
			authenticatedHandlers.GET("/credit_check_form", getCreditCheckFormByLodgerID(s.service))
			authenticatedHandlers.DELETE("/credit_check_form/:credit_check_form_id", deleteCreditCheckForm(s.service))
			authenticatedHandlers.PUT("/credit_check_form/:credit_check_form_id", updateCreditCheckForm(s.service))
			authenticatedHandlers.POST("/credit_check_form/upload_credit_check_form/:credit_check_form_id", uploadCreditCheckForm(s.service))
			authenticatedHandlers.POST("/credit_check_form/upload_credit_check_form/:credit_check_form_id/:attachment_name", uploadPDF(s.service))
			authenticatedHandlers.GET("/credit_check_form/attachment/:credit_check_form_id/:attachment_name/:booking_id", getCreditCheckFormAttachment(s.service))

			authenticatedHandlers.POST("/offer_bids/send_bits_offer", addOfferRequest(s.service))
			authenticatedHandlers.POST("/offer_bids/:bids_offer_id/accept", acceptBidsOffer(s.service))
			authenticatedHandlers.POST("/offer_bids/:bids_offer_id/reject", rejectBidsOffer(s.service))
			authenticatedHandlers.POST("/offer_bids/:bids_offer_id/cancel", cancelBidzOffer(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/offer_bids", getAllBidsOffer(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/offer_bids/equipper", getAllBidsOfferByEquipperID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/offer_bids/lodger", getAllBidsOfferByLodgerID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/offer_bids/:offer_id", getBidsOfferID(s.service))

			authenticatedHandlers.POST("/tooler_bidz_equipment", addToolerBidzEquipment(s.service))
			authenticatedHandlers.GET("/tooler_bidz_equipment", getAllToolerBidzEquipment(s.service))
			authenticatedHandlers.PUT("/tooler_bidz_equipment", updateToolerBidzEquipment(s.service))
			authenticatedHandlers.DELETE("/tooler_bidz_equipment/:tooler_bidz_equipment_id", deleteToolerBidzEquipment(s.service))
			authenticatedHandlers.POST("/tooler_bidz_equipment/:tooler_bidz_equipment_id", uploadEquipmentPhoto(s.service))
			authenticatedHandlers.DELETE("/tooler_bidz_equipment/all", dropToolerBidzEquipments(s.service))

			// promo
			authenticatedHandlers.POST("/promotion", createPromotionCode(s.service))
			authenticatedHandlers.DELETE("/promotion/:code", deletePromotionCode(s.service))
			authenticatedHandlers.GET("/promotion", getAllPromotionCodeByEquipperID(s.service))
			authenticatedHandlers.GET("/promotion/:equipper_id", getPromotionCodeByEquipperIDAndLodgerID(s.service))
		}
	}
}

// WithPubSub sets the pubsub handler.
func WithPubSub() Options {
	return func(s *Server) {
		s.handler.POST("/", uploadEquipmentsFromFileNotification(s.service))
	}
}

func WithCron() Options {
	return func(s *Server) {
		s.handler.POST("/", processEquipmentBookingsAndNotifyEquippers(s.service))
	}
}
