package main

import (
	"context"
	"errors"
	"fmt"
	"log"

	firebase "firebase.google.com/go/v4"
	"google.golang.org/api/iterator"

	"github.com/vima-inc/derental/config"
)

func main() {
	c, err := config.New()
	if err != nil {
		log.Fatalf("unable to init config %+v", err)
	}

	conf := &firebase.Config{ProjectID: c.ProjectID}

	ctx := context.Background()

	app, err := firebase.NewApp(ctx, conf)
	if err != nil {
		log.Fatalf("unable to create firebase app: %v", err)
	}

	client, err := app.Firestore(ctx)
	if err != nil {
		log.Fatalf("unable to create firestore client: %v", err)
	}

	refs := client.Collection("equipments").DocumentRefs(ctx)
	if err != nil {
		log.Fatalf("unable to get equipments: %v", err)
	}

	total := 0
	for {
		ref, err := refs.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			fmt.Errorf("unable to get next ref: %v", err)
			continue
		}

		go func() {
			_, err = client.Collection("equipments").Doc(ref.ID).Delete(ctx)
			if err != nil {
				fmt.Errorf("unable to delete equipment: %v", err)
				return
			}

			fmt.Println("deleted equipment: ", ref.ID)
		}()

		total++
	}

	fmt.Println("total deleted: ", total)
}
