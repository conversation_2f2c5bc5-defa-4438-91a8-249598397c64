package service

import (
	"github.com/vima-inc/derental/auth"
	"github.com/vima-inc/derental/broker"
	"github.com/vima-inc/derental/db"
	"github.com/vima-inc/derental/mailer"
	"github.com/vima-inc/derental/notifier"
	"github.com/vima-inc/derental/payment"
	"github.com/vima-inc/derental/pdf"
	"github.com/vima-inc/derental/storage"
)

const (
	// googleStoragePrivateInformationBucket is the bucket name for private information.
	googleStoragePrivateInformationBucket = "private_informations"

	stripePubSubTopic = "stripe-webhook"

	templatrInvoiceFR = "19eb31e2-1d9a-4660-a11a-42afcd81f303"
	templatrInvoiceEN = "9c77447d-7c80-4a88-b676-e8bece83ba82"
)

type cache interface {
	Set(key, value interface{}, cost int64) bool
	Get(key interface{}) (interface{}, bool)
	Del(key interface{})
}

// Options is the to be used by the service.
type Options func(*Service)

// WithDB sets the db service.
func WithDB(db db.Database) Options {
	return func(s *Service) {
		s.db = db
	}
}

// WithStorage sets the storage service.
func WithStorage(storage storage.Storage) Options {
	return func(s *Service) {
		s.storage = storage
	}
}

// WithMailer sets the mailer service.
func WithMailer(mailer mailer.Mailer) Options {
	return func(s *Service) {
		s.mailer = mailer
	}
}

// WithAuth sets the auth service.
func WithAuth(auth auth.Auth) Options {
	return func(s *Service) {
		s.auth = auth
	}
}

// WithNotifier sets the notifier service.
func WithNotifier(notifier notifier.Notifier) Options {
	return func(s *Service) {
		s.notifier = notifier
	}
}

// WithNotifier sets the notifier service.
func WithFrontendURL(frontendURL string) Options {
	return func(s *Service) {
		s.frontendURL = frontendURL
	}
}

// WithAirtableAPIKey sets the airtable api key.
func WithAirtableAPIKey(airtableAPIKey string) Options {
	return func(s *Service) {
		s.airtableAPIKey = airtableAPIKey
	}
}

// WithBookingNotificationEmail sets the booking notification email.
func WithBookingNotificationEmail(bookingNotificationEmail string) Options {
	return func(s *Service) {
		s.bookingNotificationEmail = bookingNotificationEmail
	}
}

// WithSendGridSender sets the sendgrid sender.
func WithSendGridSender(sendGridSender string) Options {
	return func(s *Service) {
		s.sendGridSender = sendGridSender
	}
}

// WithEquipmentLibraryURL sets the equipment library url.
func WithEquipmentLibraryURL(equipmentLibraryURL string) Options {
	return func(s *Service) {
		s.equipmentLibraryURL = equipmentLibraryURL
	}
}

// WithPayment sets the payment service.
func WithPayment(payment payment.Payment) Options {
	return func(s *Service) {
		s.payment = payment
	}
}

// WithProjectID sets the project id.
func WithProjectID(projectID string) Options {
	return func(s *Service) {
		s.projectID = projectID
	}
}

// WithBroker sets the broker service.
func WithBroker(broker broker.Broker) Options {
	return func(s *Service) {
		s.broker = broker
	}
}

// WithCache sets the cache service.
func WithCache(cache cache) Options {
	return func(s *Service) {
		s.cache = cache
	}
}
func WithTranslator(key string) Options {
	return func(s *Service) {
		s.googleTranslationApiKey = key
	}
}

// WithPDFGenerator sets the pdf generator service.
func WithPDFGenerator(pdfGenerator pdf.PDF) Options {
	return func(s *Service) {
		s.pdfGenerator = pdfGenerator
	}
}

// Service is a struct representing a Service.
type Service struct {
	db                       db.Database
	auth                     auth.Auth
	mailer                   mailer.Mailer
	storage                  storage.Storage
	notifier                 notifier.Notifier
	payment                  payment.Payment
	broker                   broker.Broker
	pdfGenerator             pdf.PDF
	airtableAPIKey           string
	frontendURL              string
	equipmentLibraryURL      string
	bookingNotificationEmail string
	sendGridSender           string
	projectID                string
	cache                    cache
	googleTranslationApiKey  string
}

// New creates a new Service instance.
func New(options ...Options) *Service {
	s := &Service{}
	for _, o := range options {
		o(s)
	}

	return s
}

func (s *Service) getPrivateInformationBucket() string {
	return s.projectID + "_" + googleStoragePrivateInformationBucket
}
