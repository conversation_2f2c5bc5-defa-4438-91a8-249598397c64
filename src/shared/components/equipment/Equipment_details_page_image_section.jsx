import {
  Box,
  Button,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableRow,
  Typography
} from '@mui/material';
import React, { useState } from 'react';
import { equipmentsDetails } from '../../helpers/Data_helper.js';

export default function EquipmentDetailsPageImageSection({
  token,
  handleShowBooking,
  setShowSignIn,
  equipment,
  t,
  detectedLanguage,
  role
}) {
  // Early return if equipment data is missing
  if (!equipment) {
    return null;
  }

  // Equipment specification data
  const specificationData = [
    {
      label: t('Description'),
      value:
        detectedLanguage === 'fr'
          ? equipment?.description_fr
          : equipment?.description || 'N/A'
    },
    { label: t('Status'), value: equipment?.status || 'N/A' },
    {
      label: t('Category'),
      value: equipment?.category ? equipment.category.join(', ') : 'N/A'
    },
    {
      label: t('Sub_category'),
      value: equipment?.sub_category ? equipment.sub_category.join(', ') : 'N/A'
    }
  ];

  // Pricing data
  const pricingData = [
    {
      period: t('Day'),
      price: equipment?.price
        ? `${equipment.price.day || 0} ${equipment.price.currency || ''}`
        : 'N/A'
    },
    {
      period: t('Week'),
      price: equipment?.price
        ? `${equipment.price.week || 0} ${equipment.price.currency || ''}`
        : 'N/A'
    },
    {
      period: t('Four_week'),
      price: equipment?.price
        ? `${equipment.price.month || 0} ${equipment.price.currency || ''}`
        : 'N/A'
    }
  ];

  // State to manage the visibility of additional specs
  const [showMoreSpecs, setShowMoreSpecs] = useState(false);

  // Function to toggle the visibility of additional specs
  const toggleMoreSpecs = () => {
    setShowMoreSpecs((prev) => !prev);
  };

  // Get additional equipment details
  const additionalSpecs = equipmentsDetails(t, equipment);
  console.log('additionalSpecs---->', additionalSpecs);
  return (
    <Paper
      elevation={8}
      sx={{
        width: '100%',
        maxWidth: 400,
        borderRadius: 6,
        p: { xs: 2, md: 3 },
        border: 1,
        borderColor: '#e5ecf6',
        display: 'flex',
        flexDirection: 'column',
        gap: 2.5,
        bgcolor: 'white',
        overflow: 'visible',
        boxShadow:
          '0 8px 32px rgba(6, 28, 61, 0.12), 0 2px 8px rgba(6, 28, 61, 0.08)',
        transition: 'box-shadow 0.3s ease, transform 0.2s ease',
        '&:hover': {
          boxShadow:
            '0 12px 40px rgba(6, 28, 61, 0.16), 0 4px 12px rgba(6, 28, 61, 0.12)',
          transform: 'translateY(-2px)'
        }
      }}
    >
      <Box
        display="flex"
        justifyContent="center"
        width="100%"
        sx={{
          background: 'rgba(135, 135, 135, 0.31)',
          borderRadius: 3,
          p: 2.5,
          mb: 1.5,
          boxShadow:
            '0 4px 16px rgba(6, 28, 61, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(135, 135, 135, 0.31)',
            pointerEvents: 'none'
          }
        }}
      >
        <Typography
          variant="h6"
          color="white"
          textAlign="center"
          sx={{
            fontSize: { xs: '1.1rem', md: '1.3rem' },
            fontWeight: 700,
            letterSpacing: '0.5px',
            textShadow: '0 2px 8px rgba(0,0,0,0.4)',
            position: 'relative',
            zIndex: 1
          }}
        >
          {t('Equipment_specifications')}
        </Typography>
      </Box>

      <Box
        sx={{
          border: 1,
          borderColor: '#e5ecf6',
          borderRadius: 4,
          p: 2.5,
          display: 'flex',
          flexDirection: 'column',
          width: '100%',
          overflow: 'visible',
          background: 'linear-gradient(to bottom, #ffffff 0%, #fafbfc 100%)',
          boxShadow:
            '0 2px 12px rgba(6, 28, 61, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.8)',
          transition: 'box-shadow 0.2s ease',
          '&:hover': {
            boxShadow:
              '0 4px 16px rgba(6, 28, 61, 0.12), inset 0 1px 0 rgba(255, 255, 255, 0.8)'
          }
        }}
      >
        <Table size="small">
          <TableBody>
            {specificationData.slice(0, 6).map((row, index) => (
              <TableRow
                key={index}
                sx={{
                  borderBottom: index < 5 ? 1 : 0,
                  borderColor: 'rgba(8, 29, 60, 0.08)',
                  '&:hover': {
                    backgroundColor: 'rgba(6, 28, 61, 0.04)',
                    transition: 'all 0.2s ease',
                    transform: 'scale(1.005)',
                    boxShadow: '0 2px 8px rgba(6, 28, 61, 0.1)'
                  },
                  '&:nth-of-type(even)': {
                    backgroundColor: 'rgba(248, 250, 252, 0.6)'
                  },
                  transition: 'all 0.2s ease'
                }}
              >
                <TableCell sx={{ border: 0, py: 2.5, width: '50%' }}>
                  <Typography
                    variant="body2"
                    color="textSecondary"
                    sx={{
                      wordWrap: 'break-word',
                      fontSize: { xs: '0.85rem', md: '0.95rem' },
                      fontWeight: 500,
                      color: '#42526B',
                      letterSpacing: '0.25px',
                      lineHeight: 1.4
                    }}
                  >
                    {row.label}
                  </Typography>
                </TableCell>
                <TableCell
                  sx={{ border: 0, py: 2.5, textAlign: 'right', width: '50%' }}
                >
                  <Typography
                    variant="body2"
                    color="textPrimary"
                    sx={{
                      wordWrap: 'break-word',
                      fontSize: { xs: '0.85rem', md: '0.95rem' },
                      color: '#061C3D',
                      fontWeight: 600,
                      letterSpacing: '0.15px',
                      lineHeight: 1.4
                    }}
                  >
                    {row.value ? row.value : 'N/A'}
                  </Typography>
                </TableCell>
              </TableRow>
            ))}
            {specificationData[5]?.label === t('Sub-category') &&
              showMoreSpecs && (
                <TableRow
                  sx={{
                    borderBottom: 1,
                    borderColor: 'rgba(8, 29, 60, 0.08)'
                  }}
                >
                  <TableCell sx={{ border: 0, py: 1.5 }} colSpan={2}>
                    {/* This TableRow is added to display a final black line separator */}
                  </TableCell>
                </TableRow>
              )}
            {showMoreSpecs &&
              additionalSpecs.map((spec, index) => {
                const hasValue =
                  spec.value1.value || (spec.value2 && spec.value2.value);
                if (hasValue) {
                  return (
                    <React.Fragment key={`additional-${index}`}>
                      <TableRow
                        sx={{
                          borderBottom: 1,
                          borderColor: 'rgba(8, 29, 60, 0.08)',
                          '&:hover': {
                            backgroundColor: 'rgba(6, 28, 61, 0.04)',
                            transition: 'all 0.2s ease'
                          },
                          transition: 'all 0.2s ease'
                        }}
                      >
                        <TableCell sx={{ border: 0, py: 2, width: '50%' }}>
                          <Typography
                            variant="body2"
                            color="textSecondary"
                            sx={{
                              wordWrap: 'break-word',
                              fontSize: { xs: '0.8rem', md: '0.9rem' },
                              fontWeight: 500,
                              color: '#42526B',
                              letterSpacing: '0.25px',
                              lineHeight: 1.4
                            }}
                          >
                            {spec.value1.label}
                          </Typography>
                        </TableCell>
                        <TableCell
                          sx={{
                            border: 0,
                            py: 2,
                            textAlign: 'right',
                            width: '50%'
                          }}
                        >
                          <Typography
                            variant="body2"
                            color="textPrimary"
                            sx={{
                              wordWrap: 'break-word',
                              fontSize: { xs: '0.8rem', md: '0.9rem' },
                              fontWeight: 600,
                              color: '#061C3D',
                              letterSpacing: '0.15px',
                              lineHeight: 1.4
                            }}
                          >
                            {spec.value1.value}
                          </Typography>
                        </TableCell>
                      </TableRow>
                      {spec.value2 && spec.value2.value && (
                        <TableRow
                          sx={{
                            borderBottom: 1,
                            borderColor: 'rgba(8, 29, 60, 0.08)',
                            '&:hover': {
                              backgroundColor: 'rgba(6, 28, 61, 0.04)',
                              transition: 'all 0.2s ease'
                            },
                            transition: 'all 0.2s ease'
                          }}
                        >
                          <TableCell sx={{ border: 0, py: 2, width: '50%' }}>
                            <Typography
                              variant="body2"
                              color="textSecondary"
                              sx={{
                                wordWrap: 'break-word',
                                fontSize: { xs: '0.8rem', md: '0.9rem' },
                                fontWeight: 500,
                                color: '#42526B',
                                letterSpacing: '0.25px',
                                lineHeight: 1.4
                              }}
                            >
                              {spec.value2.label}
                            </Typography>
                          </TableCell>
                          <TableCell
                            sx={{
                              border: 0,
                              py: 2,
                              textAlign: 'right',
                              width: '50%'
                            }}
                          >
                            <Typography
                              variant="body2"
                              color="textPrimary"
                              sx={{
                                wordWrap: 'break-word',
                                fontSize: { xs: '0.8rem', md: '0.9rem' },
                                fontWeight: 600,
                                color: '#061C3D',
                                letterSpacing: '0.15px',
                                lineHeight: 1.4
                              }}
                            >
                              {spec.value2.value}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      )}
                    </React.Fragment>
                  );
                }
                return null;
              })}
          </TableBody>
        </Table>
        {additionalSpecs.some(
          (spec) => spec.value1?.value || (spec.value2 && spec.value2?.value)
        ) && (
          <Box display="flex" justifyContent="center" mt={2}>
            <Button
              color="primary"
              endIcon={showMoreSpecs ? '⬆' : '⬇'}
              sx={{
                textTransform: 'none',
                fontSize: { xs: '0.85rem', md: '0.9rem' },
                fontWeight: 500,
                color: '#061C3D',
                padding: '8px 16px',
                borderRadius: 2,
                transition: 'all 0.2s ease',
                '&:hover': {
                  backgroundColor: 'rgba(6, 28, 61, 0.08)',
                  transform: 'translateY(-1px)'
                }
              }}
              onClick={toggleMoreSpecs}
            >
              {showMoreSpecs ? t('View_less') : t('View_more')}
            </Button>
          </Box>
        )}
      </Box>

      <Box
        sx={{
          background: 'linear-gradient(135deg, #0a2347db 0%, #0a2347db 100%)',
          borderRadius: 4,
          p: 3,
          border: '1px solid #e5ecf6',
          boxShadow:
            '0 6px 24px rgba(6, 28, 61, 0.2), 0 2px 8px rgba(6, 28, 61, 0.1)',
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background:
              'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%)',
            pointerEvents: 'none'
          }
        }}
      >
        <Stack
          direction="row"
          justifyContent="space-between"
          width="100%"
          spacing={1}
        >
          {pricingData.map((item, index) => (
            <Box
              key={index}
              display="flex"
              flexDirection="column"
              gap={1}
              sx={{
                textAlign: 'center',
                p: 2,
                borderRadius: 3,
                background: 'rgba(255, 255, 255, 0.08)',
                border: '1px solid rgba(255, 255, 255, 0.12)',
                backdropFilter: 'blur(8px)',
                position: 'relative',
                zIndex: 1,
                transition: 'all 0.3s ease',
                '&:hover': {
                  background: 'rgba(255, 255, 255, 0.12)',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 8px 24px rgba(0, 0, 0, 0.2)'
                }
              }}
            >
              <Typography
                variant="body1"
                color="textPrimary"
                sx={{
                  fontSize: { xs: '0.8rem', md: '0.9rem' },
                  fontWeight: 500,
                  color: 'rgba(255, 255, 255, 0.9)',
                  letterSpacing: '0.5px',
                  textTransform: 'uppercase',
                  lineHeight: 1.2
                }}
              >
                {item.period}
              </Typography>
              <Box
                display="flex"
                alignItems="center"
                justifyContent="center"
                flexDirection="column"
              >
                <Typography
                  variant="h6"
                  color="secondary"
                  sx={{
                    fontSize: { xs: '1.2rem', md: '1.4rem' },
                    color: '#ECA869',
                    fontWeight: 700,
                    textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                    lineHeight: 1.1,
                    fontFamily: '"airbnb-cereal-bold", sans-serif'
                  }}
                >
                  {item.price.split(' ')[0]}
                </Typography>
                {item.price.split(' ')[1] && (
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: { xs: '0.7rem', md: '0.75rem' },
                      color: 'rgba(255, 255, 255, 0.8)',
                      fontWeight: 500,
                      letterSpacing: '0.25px'
                    }}
                  >
                    {item.price.split(' ')[1]}
                  </Typography>
                )}
              </Box>
            </Box>
          ))}
        </Stack>
      </Box>

      <Button
        variant="contained"
        color="primary"
        fullWidth
        endIcon="⬅"
        sx={{
          borderRadius: 3,
          textTransform: 'none',
          height: 48,
          fontSize: { xs: '0.95rem', md: '1.1rem' },
          fontWeight: 600,
          letterSpacing: '0.5px',
          background:
            role === 'equipper'
              ? 'linear-gradient(135deg, #9e9e9e 0%, #757575 100%)'
              : 'linear-gradient(135deg, #ECA869 0%, #d4956b 100%)',
          boxShadow:
            role === 'equipper'
              ? '0 4px 12px rgba(158, 158, 158, 0.3)'
              : '0 6px 20px rgba(236, 168, 105, 0.4), 0 2px 8px rgba(236, 168, 105, 0.2)',
          border: 'none',
          transition: 'all 0.3s ease',
          '&:hover': {
            background:
              role === 'equipper'
                ? 'linear-gradient(135deg, #9e9e9e 0%, #757575 100%)'
                : 'linear-gradient(135deg, #d4956b 0%, #c8895f 100%)',
            transform: role === 'equipper' ? 'none' : 'translateY(-2px)',
            boxShadow:
              role === 'equipper'
                ? '0 4px 12px rgba(158, 158, 158, 0.3)'
                : '0 8px 24px rgba(236, 168, 105, 0.5), 0 4px 12px rgba(236, 168, 105, 0.3)'
          },
          '&:disabled': {
            background: 'linear-gradient(135deg, #9e9e9e 0%, #757575 100%)',
            color: 'rgba(255, 255, 255, 0.7)',
            cursor: 'not-allowed'
          }
        }}
        disabled={role === 'equipper'}
        onClick={token ? () => handleShowBooking() : () => setShowSignIn(true)}
      >
        {role === 'equipper'
          ? t('You_need_to_sign_in_as_a_renter')
          : t('Book_now')}
      </Button>
    </Paper>
  );
}
