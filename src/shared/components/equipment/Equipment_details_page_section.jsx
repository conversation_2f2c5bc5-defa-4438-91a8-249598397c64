import {
  Avatar,
  Box,
  Card,
  CardContent,
  Chip,
  Paper,
  Stack,
  Typography
} from '@mui/material';
import React from 'react';

export default function EquipmentDetailsPageSection({
  detectedLanguage,
  equipment,
  equipper,
  t
}) {
  // Early return if required data is missing
  if (!equipment || !equipper) {
    return null;
  }

  // Data for the equipment details
  const equipmentData = {
    title: detectedLanguage === 'fr' ? equipment?.name_fr : equipment?.name,
    company: {
      name: equipper?.company || 'N/A',
      location: equipper?.address
        ? `${equipper.address.city || ''}, ${equipper.address.state || ''}`
        : 'N/A',
      avatar: equipper?.photo_url
    },
    availability: {
      date: equipment?.available_from
        ? new Date(equipment.available_from).toLocaleDateString(
            detectedLanguage === 'fr' ? 'fr-FR' : 'en-US'
          )
        : 'N/A',
      minRentalPeriod: equipment?.minimum_rental_period || 'N/A'
    },
    image: equipment?.image_link
  };

  return (
    <Paper
      elevation={0}
      sx={{
        p: { xs: 2, sm: 4, md: 6 },
        borderRadius: { xs: '15px', md: '30px' },
        bgcolor: 'white',
        width: '100%'
      }}
    >
      <Stack spacing={3.5}>
        <Box>
          <Stack spacing={4}>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems={{ xs: 'flex-start', md: 'center' }}
              flexDirection={{ xs: 'column', md: 'row' }}
              gap={{ xs: 2, md: 0 }}
            >
              <Typography
                variant="h3"
                fontWeight="bold"
                sx={{
                  fontSize: { xs: '1.8rem', sm: '2.2rem', md: '3rem' },
                  lineHeight: { xs: '2rem', sm: '2.5rem', md: '48px' },
                  wordBreak: 'break-word',
                  hyphens: 'auto'
                }}
              >
                {equipmentData.title}
              </Typography>

              <Card
                variant="outlined"
                sx={{
                  p: { xs: 1, md: 1.5 },
                  borderRadius: 2,
                  boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.05)',
                  border: '1px solid #e5ecf6',
                  display: 'flex',
                  alignItems: 'center',
                  gap: { xs: 1, md: 2 },
                  width: { xs: '100%', md: 'auto' },
                  minWidth: { xs: 'auto', md: '200px' }
                }}
              >
                <Avatar
                  src={equipmentData.company.avatar}
                  sx={{
                    width: { xs: 35, md: 42.67 },
                    height: { xs: 35, md: 43 },
                    borderRadius: '10.58px'
                  }}
                />
                <CardContent sx={{ p: 0, flex: 1 }}>
                  <Typography
                    variant="subtitle1"
                    fontWeight="600"
                    color="text.primary"
                    sx={{
                      fontSize: { xs: '0.9rem', md: '1rem' },
                      wordBreak: 'break-word'
                    }}
                  >
                    {equipmentData.company.name}
                  </Typography>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{
                      fontSize: { xs: '0.8rem', md: '0.875rem' },
                      wordBreak: 'break-word'
                    }}
                  >
                    {equipmentData.company.location}
                  </Typography>
                </CardContent>
              </Card>
            </Box>

            <Stack
              direction={{ xs: 'column', sm: 'row' }}
              spacing={2}
              alignItems={{ xs: 'stretch', sm: 'center' }}
            >
              <Chip
                label={`${t('Available_from')} : ${
                  equipmentData?.availability.date
                }`}
                variant="outlined"
                sx={{
                  borderRadius: '140px',
                  borderColor: 'info.main',
                  color: 'info.main',
                  px: 1.5,
                  py: 0.5,
                  height: 'auto',
                  fontSize: { xs: '0.8rem', md: '0.875rem' },
                  width: { xs: '100%', sm: 'auto' }
                }}
              />
              <Chip
                label={`${t('Minimum_rental_period')} : ${
                  equipmentData?.availability.minRentalPeriod
                }`}
                variant="outlined"
                sx={{
                  borderRadius: '140px',
                  borderColor: 'warning.main',
                  color: 'warning.main',
                  px: 1.5,
                  py: 0.5,
                  height: 'auto',
                  fontSize: { xs: '0.8rem', md: '0.875rem' },
                  width: { xs: '100%', sm: 'auto' }
                }}
              />
            </Stack>
          </Stack>
        </Box>

        <Box
          sx={{
            width: '100%',
            height: { xs: 250, sm: 300, md: 403 },
            backgroundImage: `url(${equipmentData.image})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            borderRadius: { xs: 2, md: 3 }
          }}
        />
      </Stack>
    </Paper>
  );
}
