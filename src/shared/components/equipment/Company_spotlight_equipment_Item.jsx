import React from 'react';
import { getCookies } from '../../helpers/Cookies';
import { priceData } from '../../helpers/Data_helper';
import FormatPrice from '../../helpers/Price_helper';
import { cutString, isEmptyValue } from '../../helpers/String_helps';
import CustomImage from '../images/Custom_image';
import RenderIf from '../Render_if';
import CustomTooltip from '../tooltips/Tooltip';
import { useNavigate } from 'react-router-dom';

export default function CompanySpotlightEquipmentItem({
  selectedEquipment,
  detectLanguage,
  setShowSignIn,
  promotion,
  isOpen,
  item,
  t,
  isEquipperSpotlight
}) {
  const role = getCookies('role');
  const token = getCookies('token');
  const navigate = useNavigate();

  const bookNow = () => {
    selectedEquipment(item);
    isOpen(true);
  };

  return (
    <div className="col-lg-4 col-md-4 col-sm-12 margin-bottom">
      <div class="badge-area-show">
        <RenderIf condition={promotion?.percent_off}>
          <div class="bagde-flag-wrap">
            <p class="bagde-flag c-blue-grey ">{promotion?.percent_off} %</p>
          </div>
        </RenderIf>
        <div className="result-box with-border h-lg-100 relative">
          <div
            className={`col-lg-12 d-flex ${
              promotion?.percent_off ? 'justify-content-end' : ''
            }`}
          >
            <p
              className={`t-body-small  m-0 ${
                item.minimum_rental_period
                  ? 'c-blue-grey minimum_rental_period '
                  : 'c-white m-2'
              }`}
            >
              <strong
                className={`t-body-small    ${
                  item.minimum_rental_period ? 'c-fake-black' : 'hidden'
                }`}
              >
                {t('Minimum_rental_period')} :{' '}
              </strong>
              {item.minimum_rental_period} {t('Day')}(s)
            </p>
          </div>

          <div className="d-flex align-content-lg-center-items-cente justify-content-between margin-lr-0">
            <div className="result-box__imageContent col-6 col-lg-5 h-100 d-lg-flex flex-column justify-content-between">
              <div className="result-box__image mt-2">
                <CustomImage
                  imageUrl={
                    item.equipper_equipment_picture &&
                    !item.equipper_equipment_picture.includes(
                      'equipment_library/Empty_state_equipment.png'
                    )
                      ? item.equipper_equipment_picture
                      : item.image_link
                  }
                  className="w-75"
                  alt={t('Cant_load_image')}
                />
              </div>
            </div>
            <div className="result-box__rightPart col-6 col-lg-7 p-0">
              <div className={isEquipperSpotlight ? 'col-12' : ''}>
                <div className="result-box__top">
                  <div className="result-box__top-left">
                    <CustomTooltip
                      text={detectLanguage === 'fr' ? item.name_fr : item.name}
                    >
                      <h2 className="t-subheading-2 c-fake-black">
                        {item.preferred_equipment_name
                          ? cutString(item.preferred_equipment_name, 28)
                          : cutString(item.name, 28)}
                      </h2>
                    </CustomTooltip>

                    <div className="row">
                      <div
                        className={`result-left-bottom col-lg-12 ${
                          isEquipperSpotlight ? 'col-lg-12' : ''
                        }`}
                      >
                        <p className="t-body-small c-blue-grey">
                          <strong className="t-body-small bold d-block">
                            {t('Description')} :
                          </strong>
                          <CustomTooltip
                            text={
                              detectLanguage === 'fr'
                                ? item.description_fr
                                : item.description
                            }
                            placement="bottom"
                          >
                            <p>
                              {isEmptyValue(
                                detectLanguage === 'fr'
                                  ? cutString(item?.description_fr, 15)
                                  : cutString(item?.description, 15)
                              )}
                            </p>
                          </CustomTooltip>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-lg-12  d-lg-block d-none">
                <div className="top-content ">
                  <div className="catalogue-prices flex w-100 mt-2 mr-4 mb-4">
                    {priceData(item, t).map(
                      (item, key) =>
                        item.name !== 'delivery_drop_coast' && (
                          <div
                            className={`catalogue-prices--content c-near-grey padding-right-10 ${
                              item.name === 'month' ? 'col-5' : 'col-4'
                            }`}
                            key={key}
                          >
                            <span className="d-block t-caption-small c-neutrals-gray">
                              {item.placeholder}
                            </span>
                            <p className="c-white t-subheading-3 bold c-fake-black">
                              {FormatPrice(item.value)}
                            </p>
                            <span className="d-block t-caption-small  c-fake-black ">
                              {item.currency?.toUpperCase()}
                            </span>
                          </div>
                        )
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="row d-lg-none d-flex mt-3 mb-3">
            <div className="col-12">
              <div className="top-content ">
                <div className="catalogue-prices flex w-100 mt-2 mr-4 mb-2">
                  {priceData(item, t).map(
                    (item, key) =>
                      item.name !== 'delivery_drop_coast' && (
                        <div
                          className="catalogue-prices--content c-near-grey col-4"
                          key={key}
                        >
                          <span className="d-block t-caption-small c-neutrals-gray">
                            {item.placeholder}
                          </span>
                          <p className="c-white t-subheading-2 bold c-fake-black">
                            {FormatPrice(item.value)}
                          </p>
                          <span className="d-block t-caption-small">
                            {item.currency?.toUpperCase()}
                          </span>
                        </div>
                      )
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className="w-100 d-block lg-absolute">
            <div className="button-content">
              <button
                disabled={role === 'equipper'}
                className={`round-button yellow bold c-white d-inline-flex align-items-center justify-content-center ${
                  role === 'equipper' ? 'c-near-grey' : 'hover_black'
                }`}
                onClick={token ? () => bookNow() : () => setShowSignIn(true)}
              >
                {t('Book_now')}
              </button>
              <button
                className="round-button blue bold c-white d-inline-flex align-items-center justify-content-center view-more-btn hover_black"
                onClick={() =>
                  navigate(
                    `/equipmentDetails/${item.objectID}_${item.equipper_id}`
                  )
                }
              >
                {t('View_details')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
