import React, { useEffect, useRef, useState } from 'react';
import { Configure } from 'react-instantsearch-dom';
import qs from 'qs';
import {
  urlToSearchState,
  searchStateToUrl,
  createURL
} from '../../shared/helpers/Algolia_helper';
import { InfiniteHitsDropdown } from '../../components/search_result/Infinite_hits_dropdown';
import { CustomSearchBox } from '../../components/search_result/Custom_search_box';
import InstantSearchAlgolia from '../../components/search_result/Instant_search_algolia';
import RenderIf from '../../shared/components/Render_if';

export default function SearchInputAutoCompleteEquipment({
  attribute,
  placeholder,
  value,
  onChange,
  isEquipment,
  indexName,
  isExploreMore,
  className,
  setEquipmentID,
  detectLanguage,
  onLocationChange,
  isAdvancedFilter,
  equipmentNameClassName,
  t,
  isAddEquipment
}) {
  const [searchState, setSearchState] = useState(() =>
    urlToSearchState(window.location, qs)
  );
  const timerRef = useRef(null);
  const [show, setShow] = useState(false);

  useEffect(() => {
    clearTimeout(timerRef.current);
    timerRef.current = setTimeout(() => {
      window.history.pushState(
        searchState,
        null,
        searchStateToUrl({ location: window.location }, searchState, qs)
      );
    }, 400);
  }, [searchState]);
  return (
    <InstantSearchAlgolia
      indexName={indexName}
      searchState={searchState}
      onSearchStateChange={setSearchState}
      createURL={(searchState) => createURL(searchState, qs)}
    >
      <Configure hitsPerPage={15} />
      <ul className="siac">
        <CustomSearchBox
          className={className || 'search-input-auto-complete width-100'}
          placeholder={placeholder}
          onChange={onChange}
          value={value}
          showDropdown={setShow}
          setEquipmentID={setEquipmentID}
          attribute={attribute}
        />
        <RenderIf condition={show}>
          <InfiniteHitsDropdown
            attribute={attribute}
            onChange={onChange}
            t={t}
            isExploreMore={isExploreMore}
            equipmentNameClassName={equipmentNameClassName}
            detectLanguage={detectLanguage}
            isEquipment={isEquipment}
            setEquipmentID={setEquipmentID}
            onLocationChange={onLocationChange}
            isAdvancedFilter={isAdvancedFilter}
            isAddEquipment={isAddEquipment}
          />
        </RenderIf>
      </ul>
    </InstantSearchAlgolia>
  );
}
