.RWC {
  background-color: $white;
  border-radius: 23px;
  height: 590px;
  width: 100%;
  padding: 8%;
  vertical-align: middle;
  @media (max-width: 992px) {
    margin-bottom: 25px;
    height: 540px;
  }
  @media (max-width: 768px) {
    height: auto;
    padding: 5%;
  }

  .dropdown-divider {
    margin: 20px 0;
  }

  .img-b {
    border: 1px solid #cccccc;
    width: 100%;
    height: 220px;
    padding: 0;
    border-radius: 23px;
    object-fit: cover;
    @media (min-width: 992px) {
      height: 260px;
    }
    @media (max-width: 768px) {
      height: 180px;
    }
  }
}

.title-section {
  margin-top: 55px;
  margin-bottom: 12px;
  @media (max-width: 992px) {
    font-size: 20px;
    line-height: 33px;
  }
  @media (max-width: 768px) {
    font-size: 18px;
    line-height: 30px;
  }
}

.img-b {
  width: 92%;
  padding: 0% !important;
  border-radius: 23px;
}

.img-c {
  width: 92%;
  padding: 0% !important;
  border-radius: 23px;
}

.h3-title {
  margin-top: 17px;
}

.price {
  margin-top: 20px;
  font-size: 23px;
  padding-bottom: 70px;
  @media (min-width: 992px) {
    margin-top: 0;
  }
  @media (max-width: 768px) {
    font-size: 20px;
    padding-bottom: 50px;
  }
}

.eqd-bottom-bar {
  display: flex;
  flex-direction: row;
  justify-content: start;
  margin-top: 10px;

  svg {
    position: relative;
    top: 3px;
    margin-top: 10px;
    margin-right: 0;
    width: 25px;
  }

  span {
    margin-top: 8px;
    margin-right: 6px;
  }
}

.company-logo-details {
  width: 110px;
  height: 24px;
  margin-top: 20px;
  position: absolute;
  @media (min-width: 992px) {
    margin-top: 40px;
  }
  @media (max-width: 768px) {
    width: 90px;
    height: 20px;
  }
}

.center-div {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: center;
}

.button-bn {
  border-radius: 24px;
  padding: 5px 10px;
  min-width: 70%;
  background: $yellow;
  min-height: 50px;
  color: black;
  font-weight: 700;
  border: none;
  @media (max-width: 768px) {
    min-width: 90%;
    min-height: 45px;
  }
}

.margin-top--10 {
  margin-top: -17px;
}

.margin-top-20 {
  margin-top: 20px;
}

.margin-top-5 {
  margin-top: 5px;
}

.margin-left-20 {
  margin-left: 20px;
}

.margin-left-10 {
  margin-left: 10px;
}

.RWC-text {
  background-color: $white;
  border-radius: 23px;
  width: 100%;
  padding: 35px 30px 30px;
  vertical-align: middle;
  @media (min-width: 992px) {
    padding: 55px 80px 40px;
  }
  @media (max-width: 768px) {
    padding: 25px 20px 20px;
  }

  p {
    line-height: 38px;
    font-weight: 400;

    &.no-marge {
      margin-bottom: 0;
    }
  }

  h6 {
    font-size: 20px;
    line-height: 35px;
  }
}

.tiltle2 {
  text-align: start;
  font-family: $secondary-font;
  font-weight: 700;
}

.fs39 {
  font-size: 40px;
  font-family: $secondary-font;
  font-weight: bold;
}

.toolo-marge {
  margin: 100px 0;
  @media (max-width: 992px) {
    text-align: center;
    margin: 60px 0;
  }
  @media (max-width: 768px) {
    margin: 40px 0;
  }

  &.reverse-mobile {
    .row {
      @media (max-width: 992px) {
        flex-direction: column-reverse;
      }
    }
  }

  .img-c {
    @media (max-width: 992px) {
      width: 50%;
    }
    @media (max-width: 768px) {
      width: 70%;
    }
  }
}

.maps {
  border: 1px solid #cccccc;
  border-radius: 23px;
  width: 100%;
  height: 635px;
  @media (max-width: 768px) {
    height: 400px;
  }
}

.result-search {
  .result-box {
    .bidz-equipment-item-text-area {
      border: 1px solid $check-grey;
      border-radius: 25px;
      padding: 7px 10px;
      width: 100%;
      margin-top: 20px;
    }

    .bidz-request-submit {
      margin-top: 10px;

      p {
        display: flex;
        margin-bottom: 0;

        .bidz-equipment-item {
          border: 1px solid $check-grey;
          border-radius: 50px;
          padding: 7px 10px;
          flex: 1;
        }

        .bidz-equipment-item::placeholder {
          @media (max-width: 992px) {
            font-size: 14px !important;
          }
          @media (max-width: 768px) {
            font-size: 12px !important;
          }
        }

        .title-bidz-item {
          margin-right: 10px;
        }
      }
    }

    .css-b62m3t-container {
      flex: 1;

      .css-junnbe-control,
      .css-o2oy4c-control,
      .css-1t5plm3-control {
        min-height: 35px;
        height: 35px;
        display: flex;
        align-items: center;
        padding: 0 8px;
      }
    }

    .bidz-button {
      display: block;
      text-align: center;
      @media (min-width: 768px) {
        display: inline-block;
        float: right;
      }
    }
  }
}

.progress-bar {
  color: $primary-color;
}

/*
  • Equipement details / page
  ---------- ---------- ---------- ---------- ----------
*/

.box-equipement {
  border-radius: 12px;
  border: 1px solid $border-grey;
  background: $white;
  padding: 20px;

  &__header {
    padding-bottom: 13px;
    border-bottom: 1px solid #e3e5e9;
    margin-bottom: 10px;

    img {
      width: 45px;
      height: 45px;
      border-radius: 100%;
      margin-right: 15px;
    }
  }

  &__content {
    img {
      margin-top: 10px;
    }

    p {
      margin-top: 8px;
      margin-bottom: 20px;
    }
  }

  &__prices {
    margin-bottom: 30px;

    p {
      margin: 0;
    }
  }
}

.box-technical-information {
  padding: 10px 16px;
  border: 1px solid $border-grey;
  border-radius: 14px;
  margin-top: 16px;
  &__content {
    padding: 14px 0;
    border-bottom: 1px solid $border-grey;

    &:last-child {
      border: 0;
    }
  }

  button {
    margin-top: 20px;
  }
}

.ml-equipements {
  margin-top: 55px;
  margin-bottom: 80px;
  @media (min-width: 992px) {
    margin-left: 17px;
    max-width: 700px;
    margin-top: 0;
    margin-bottom: 0;
  }
  @media (max-width: 768px) {
    margin-top: 30px;
    margin-bottom: 40px;
  }
}

.need-for-information-equipements {
  margin-top: 68px;
  @media (max-width: 768px) {
    margin-top: 34px;
  }
}

.equipment-details-page {
  .reach_out {
    display: none !important;
  }
}
.css-jvep09-MuiButtonBase-root-MuiButton-root,
.css-we7jyg-MuiButtonBase-root-MuiButton-root {
  background-color: #eca869 !important;
  &:hover {
    background-color: #d98e4e !important;
  }
  &:active {
    background-color: #b77a3a !important;
  }
  &:focus {
    background-color: #eca869 !important;
  }
  &:disabled {
    background-color: gray !important;
    color: black !important;
  }
}

.css-1dyzmf7-MuiTypography-root,
.css-151wj2f-MuiTypography-root,
.css-1kwi5j1-MuiButtonBase-root-MuiButton-root,
.css-1uiu80g-MuiTypography-root {
  color: #eca869 !important;
}
.view-more-btn {
  border-color: #eca869;
  color: black;
  margin: 0px 5px;
}
.css-co8jy2-MuiButtonBase-root-MuiButton-root,
.css-co8jy2-MuiButtonBase-root-MuiButton-root:hover {
  background-color: #eca869 !important;
}
.css-1613yot-MuiTypography-root,
css-1613yot-MuiTypography-root:hover {
  color: black !important;
}
.css-jgn6p1,
.css-eqhgup,
.css-10rxdkl,
.css-9rpm8d {
  background-size: contain !important;
}
