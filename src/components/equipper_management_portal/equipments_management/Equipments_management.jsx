import { faMinus, faPlus } from '@fortawesome/fontawesome-free-solid';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import React, { useEffect, useState } from 'react';
import { Configure, Menu } from 'react-instantsearch-dom';
import { CustomEquipmentsInfiniteHits } from '../../algolia/Equipments_infine_hits';
import {
  useEquipment,
  useFetchUniqueNames
} from '../../../shared/context/Equipment_context';
import Popup from '../../../shared/components/modals/Popup';
import SuccessPopUp from '../../../shared/components/modals/Success_pop_up';
import ConfirmationModal from '../../../shared/components/modals/Confirmation_modal';
import { getCookies, setCookies } from '../../../shared/helpers/Cookies';
import {
  getSessionStorage,
  setSessionStorage
} from '../../../shared/helpers/Session_storage_helper';
import InstantSearchAlgolia from '../../search_result/Instant_search_algolia';
import ToloIsLoading from '../../../shared/components/cards/Tolo_is_loading';
import { index } from '../../../shared/helpers/Algolia_helper';
import MinimumRentalPeriodModal from './Minimum_rental_period';
import { useNavigate } from 'react-router-dom';

export default function EquipmentManagement({ detectLanguage, t }) {
  const { DeleteAllEquipments } = useEquipment();
  const navigate = useNavigate();
  const subCategory = getSessionStorage('sub_category');
  const category = getSessionStorage('category');
  const hasInventory = getCookies('has_inventory') === 'true';
  const hasRequests = getCookies('hasRequests') === 'true';

  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [onAction, setOnAction] = useState(false);
  const [response, setResponse] = useState(false);
  const [showMRP, setShowMRP] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [show, setShow] = useState(false);
  const [searchState, setSearchState] = useState({
    menu: {
      equipper_id: getCookies('userId'),
      status: getSessionStorage('status'),
      is_active: true
    }
  });

  const equipments = useFetchUniqueNames();

  function handleAddEquipment() {
    navigate('/equipperManagementPortal/equipmentManagement/addEquipment');
  }

  function handleStateSwitch(searchState) {
    setSearchState(searchState);
  }

  function handleShow(setter, getter) {
    setter(!getter);
  }

  function handleSuccess() {
    handleShow(setShowConfirmationModal, showConfirmationModal);
    window.location.reload();
  }

  async function deleteAllEquipments() {
    setOnAction(true);
    const { hits } = await index.search('', {
      filters: `equipper_id:${getCookies('userId')}`,
      hitsPerPage: 1000
    });

    const res = await DeleteAllEquipments();
    if (res.status === 200) {
      await index.deleteObjects(hits.map((hit) => hit.objectID));
      setTimeout(() => {
        setOnAction(false);
        setShowSuccessModal(true);
        setCookies('has_inventory', false);
      }, 8000);
    } else {
      setResponse(res);
      handleShow(setShow, show);
    }
  }

  useEffect(() => {
    if (category !== '') {
      setSearchState({
        menu: {
          equipper_id: getCookies('userId'),
          category: category,
          sub_category: subCategory,
          status: getSessionStorage('status')
        }
      });
    } else {
      setSearchState({
        menu: {
          equipper_id: getCookies('userId'),
          category: '',
          sub_category: '',
          status: getSessionStorage('status')
        }
      });
    }
  }, [category, subCategory]);

  useEffect(() => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
    return () => {
      setSessionStorage('status', '');
      setSessionStorage('category', '');
      setSessionStorage('sub_category', '');
    };
  }, []);

  if (isLoading) {
    return <ToloIsLoading />;
  }

  return (
    <>
      <InstantSearchAlgolia
        searchState={searchState}
        onSearchStateChange={handleStateSwitch}
        indexName={import.meta.env.VITE_ALGOLIA_INDEX_NAME}
      >
        <Configure
          hitsPerPage={10}
          filters={`is_active:true AND equipper_id:${getCookies('userId')}`}
        />
        <Menu attribute="status" className="hidden" />
        <Menu attribute="category" className="hidden" />
        <Menu attribute="sub_category" className="hidden" />
        <div className="panel" id="p2">
          <div className="equipments-management white-bg">
            <div className="equipments-search">
              <div className="d-flex justify-content-end">
                {(!hasInventory || hasRequests) && <div className="col-lg-6" />}
                <div
                  className={hasInventory ? 'mr-10 ' : 'col-lg-6 text-lg-end'}
                >
                  <button
                    className="round-button yellow bold mb-4"
                    onClick={handleAddEquipment}
                  >
                    <FontAwesomeIcon icon={faPlus} />
                    {t('Add_equipment_button')}
                  </button>
                </div>
                {hasInventory && !hasRequests && (
                  <button
                    className="round-button yellow bold mb-4"
                    onClick={() =>
                      handleShow(
                        setShowConfirmationModal,
                        showConfirmationModal
                      )
                    }
                  >
                    <FontAwesomeIcon icon={faMinus} />
                    {t('Delete_equipment_button')}
                  </button>
                )}
              </div>
            </div>
            <CustomEquipmentsInfiniteHits
              detectLanguage={detectLanguage}
              handleStateSwitch={handleStateSwitch}
              hasInventory={hasInventory}
              mrpOptions={equipments}
              searchState={searchState}
              showMRP={() => handleShow(setShowMRP, showMRP)}
              setIsLoading={setIsLoading}
              t={t}
            />
          </div>
        </div>
      </InstantSearchAlgolia>
      <ConfirmationModal
        isLoading={onAction}
        onClose={() =>
          handleShow(setShowConfirmationModal, showConfirmationModal)
        }
        cancelText={t('Cancel')}
        message={t('Delete_all_equipments_cta')}
        action={deleteAllEquipments}
        buttonText={t('Delete')}
        show={showConfirmationModal}
        t={t}
      />

      <Popup
        t={t}
        show={show}
        onClose={() => handleShow(setShow, show)}
        response={response}
      />

      <MinimumRentalPeriodModal
        show={showMRP}
        equipments={equipments}
        handleShowSuccess={() =>
          handleShow(setShowSuccessModal, showSuccessModal)
        }
        handleShowFailure={() => handleShow(setShow, show)}
        onClose={() => handleShow(setShowMRP, showMRP)}
        t={t}
      />
      <SuccessPopUp
        show={showSuccessModal}
        onClose={() => {
          handleSuccess();
        }}
      />
    </>
  );
}
