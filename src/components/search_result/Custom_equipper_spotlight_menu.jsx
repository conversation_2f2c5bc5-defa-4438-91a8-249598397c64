import React, { useEffect, useState } from 'react';
import { connectMenu } from 'react-instantsearch-dom';
import CategoryItem from '../../shared/components/labels/Category_item';
import {
  categoriesOptions,
  status,
  subCategoriesOptions
} from '../../shared/helpers/Data_helper';
import {
  setSessionStorage,
  getSessionStorage
} from '../../shared/helpers/Session_storage_helper';

// List of always-selected categories (lowercase)
const ALWAYS_SELECTED_CATEGORIES = [
  'earthmoving',
  'elevation and scaffolding',
  'landscaping'
];

const Menu = ({
  items,
  refine,
  selected,
  attribute,
  isEquipmentManagement,
  t,
  setItemsLength,
  isEquipperSpotlight,
  isHomeInstantSearchByCategory
}) => {
  const [data, setData] = useState([]);
  const category = getSessionStorage('category');

  // Helper to check if a value is in the always-selected list
  const isAlwaysSelectedCategory = (value) =>
    ALWAYS_SELECTED_CATEGORIES.includes(
      value?.toLowerCase?.()
    );

  // If attribute is category or sub_category, and selected is not one of the always-selected, force select the first always-selected
  useEffect(() => {
    // Only auto-select if nothing is selected
    if (
      (attribute === 'category' || attribute === 'sub_category') &&
      (!selected || selected === '')
    ) {
      // Find the first available always-selected category in the data
      let availableOptions = [];
      if (attribute === 'category') {
        availableOptions =
          isEquipperSpotlight || isEquipmentManagement
            ? items
            : categoriesOptions(t);
      } else if (attribute === 'sub_category') {
        if (category !== '') {
          availableOptions = items;
        } else {
          availableOptions = subCategoriesOptions(t);
        }
      }
      const firstAlwaysSelected = availableOptions.find((item) =>
        isAlwaysSelectedCategory(item.value)
      );
      if (firstAlwaysSelected) {
        // Set session storage and refine
        if (attribute === 'category') {
          setSessionStorage('category', firstAlwaysSelected.value);
          setSessionStorage('sub_category', '');
        } else if (attribute === 'sub_category') {
          setSessionStorage('sub_category', firstAlwaysSelected.value);
        }
        refine(firstAlwaysSelected.value);
      }
    }
    // eslint-disable-next-line
  }, [attribute, items, t]);

  const refineItem = (item) => {
    switch (attribute) {
      case 'category':
        if (selected === item.value) {
          refine('');
          setSessionStorage('category', '');
          setSessionStorage('sub_category', '');
        } else {
          refine(item.value);
          setSessionStorage('category', item.value);
          setSessionStorage('sub_category', '');
        }
        break;
      case 'sub_category':
        if (selected === item.value) {
          refine('');
        } else {
          refine(item.value);
          setSessionStorage('sub_category', item.value);
        }
        break;
      case 'status':
        if (selected === item.value) {
          refine('');
          setSessionStorage('status', '');
        } else {
          refine(item.value);
          setSessionStorage('status', item.value);
        }
        break;
      default:
        refine(item.value);
    }
  };

  useEffect(() => {
    if (attribute === 'sub_category' && category !== '') {
      setItemsLength(items.length || data.length);
    }
    switch (attribute) {
      case 'category':
        if (isHomeInstantSearchByCategory) {
          // Use the ordered categories for home instant search
          setData([
            { value: 'earthmoving', label: t('Earthmoving') },
            { value: 'elevation and scaffolding', label: t('Elevation_and_scaffolding') },
            { value: 'landscaping', label: t('Landscaping') },
            { value: 'handling', label: t('Handling') },
            { value: 'specialized tooling', label: t('Specialized_tooling') },
            { value: 'energy and air', label: t('Energy_and_air') },
            { value: 'waste solution', label: t('Waste_solution') },
            { value: 'fluid solutions', label: t('Fluid_solutions') },
            { value: 'site solution', label: t('Site_solution') },
            { value: 'event and reception', label: t('Event_reception') }
          ]);
        } else {
          setData(
            isEquipperSpotlight || isEquipmentManagement
              ? items
              : categoriesOptions(t)
          );
        }
        break;
      case 'sub_category':
        if (category !== '') {
          setData(items);
        } else {
          setData(subCategoriesOptions(t));
        }
        break;
      case 'status':
        setData(status(t));
        break;
      default:
        break;
    }
  }, [attribute, items, t, isHomeInstantSearchByCategory]);

  // Find the first always-selected category in the current data
  const firstAlwaysSelected = data.find((item) =>
    isAlwaysSelectedCategory(item.value)
  );

  return (
    <>
      <ul className="p-0">
        {data.map((item, key) => (
          <CategoryItem
            onClick={() => refineItem(item)}
            selected={
              // Always select the first always-selected category if attribute is category or sub_category and nothing is selected
              (attribute === 'category' || attribute === 'sub_category')
                ? (
                    (!selected || selected === '')
                      ? (firstAlwaysSelected && item.value === firstAlwaysSelected.value)
                      : (
                        (selected &&
                          selected.toLowerCase() === item.label.toLowerCase()) ||
                        (selected && selected.toLowerCase() === item.value.toLowerCase())
                      )
                  )
                : (
                  (selected &&
                    selected.toLowerCase() === item.label.toLowerCase()) ||
                  (selected && selected.toLowerCase() === item.value.toLowerCase())
                )
            }
            item={item}
            attribute={attribute}
            category={category}
            iconPath={category?.iconPath}
            t={t}
            key={key}
          />
        ))}
      </ul>
    </>
  );
};

export const CustomSpotlightMenu = connectMenu(Menu);
