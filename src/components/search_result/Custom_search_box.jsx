import React from 'react';
import { connectSearchBox } from 'react-instantsearch-dom';

const SearchBox = ({
  value,
  placeholder,
  refine,
  className,
  onChange,
  showDropdown,
  attribute,
  setEquipmentID
}) => {
  const onInternalBlur = () => {
    setTimeout(() => {
      showDropdown(false);
    }, 300);
  };

  return (
    <li>
      <input
        type="search"
        className={className}
        placeholder={placeholder}
        value={value}
        onBlur={onInternalBlur}
        required
        onChange={(event) => {
          showDropdown(true);
          attribute === 'equipment' && onChange(event.currentTarget.value);
          attribute === 'location' &&
            onChange({ value: event.currentTarget.value, isSelected: false });
          refine(event.currentTarget.value);
        }}
        onKeyDown={() => {
          attribute === 'equipment' && setEquipmentID('');
        }}
      />
    </li>
  );
};
SearchBox.defaultProps = {
  onChange: () => {},
  showDropdown: () => {}
};

export const CustomSearchBox = connectSearchBox(SearchBox);
